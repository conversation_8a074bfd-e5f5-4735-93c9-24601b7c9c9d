workspace "Structurizr Lite" {

    !identifiers hierarchical
    !const STRUCTURIZR_LITE_HOME "/Users/<USER>/sandbox/structurizr-lite"

    model {

        appUser = person "Customer" "Apps End user (e.g., child or parent)" "person"

        portalUser = person "Backoffice User" "Portal End user (e.g., Admin or Agent..etc)" "person"

        partnerUser = person "B2B User" "Partner End user (e.g., Partner Admin or Partner Agent..etc)" "person"

        partners = softwareSystem "Partners" {
          description "External content or technology partners"

          contentConsumer = container "Content Consumer" {
            description "Content consumer partner"
          }

          usersAcquisition = container "Users Acquisition" {
            description "Users acquisition partner"

            twt = component "TimWe" {
              description "TimWe partner"
            }

            switch = component "Switch" {
              description "Switch partner"
              }

            mondia = component "Mondia" {
              description "Mondia partner"
              }

            cafeyn = component "Cafeyn" {
              description "Cafeyn as a partner"
              }

            salt = component "Salt" {
              description "Salt partner"
              }
            dv = component "Digital Virgo" {
            description "Digital Virgo partner"
            }
          }
        }

        brainTree = softwareSystem "BrainTree" {
          description "BrainTree payment gateway"
          tag "braintree"
        }

        kidjo = softwareSystem "Kidjo System" {
          description "Kidjo Software systems"
          tag " kidjo"


          s3 = container "Amazon S3" {
            description "Amazon S3 bucket"
            tag "Amazon Web Services - Simple Storage Service"
          }

          lambda = container "AWS Lambda" {
            description "Analytics lambda function"
            tag "Amazon Web Services - Lambda" "innerView"
          }

          cloudFront = container "AWS CloudFront" {
            description "CloudFront distribution"
            tag "Amazon Web Services - CloudFront"
          }

          transcoder = container "AWS Elemental MediaConvert" {
            description "MediaConvert transcoder"
            tag "Amazon Web Services - Elastic Transcoder"
          }

          rds = container "AWS RDS" {
            description "Relational database service"
            tag "Amazon Web Services - RDS"

            kidjoDB = component "Kidjo DB" {
              description "Kidjo database"
              tag "Amazon Web Services - Aurora MySQL Instance"
            }

            kidjoStoriesDB = component "Kidjo Stories DB" {
              description "Kidjo stories database"
              tag "Amazon Web Services - Aurora PostgreSQL Instance"
            }

            kidjoGamesDB = component "Kidjo Games DB" {
              description "Kidjo games database"
              tag "Amazon Web Services - Aurora PostgreSQL Instance"
            }

            kidjoBODB = component "Kidjo BackOffice DB" {
              description "Kidjo Backoffice database"
              tag "Amazon Web Services - Aurora MySQL Instance"
            }

          }

          dnsService = container "Gandi" {
              description "DNS records store the destination of kidjo sub-domains"
              tag "gandi"
          }

          alb = container "ALB" {
              description "AWS loadbalancer instances that forward the traffic to the appropriate server"
              tag "Amazon Web Services - Elastic Load Balancing Application Load Balancer"
          }

          servers = container "Backend Services" {
              description "Kidjo servers"

              kidjoAppServer = component "Kidjo Server" {
                description "Core backend server handling users/reports/partners logic and data"
                tag "Amazon Web Services - Elastic Beanstalk"
              }

              kidjoGamesServer = component "Kidjo Games Server" {
                description "Core backend server handling games logic and data"
                tag "Amazon Web Services - Elastic Beanstalk"
              }


              kidjoStoriesServer = component "Kidjo Stories Server" {
                description "Core backend server handling books/stories logic and data"
                tag "Amazon Web Services - Elastic Beanstalk"
              }

              booksBackofficeServer = component "Books Backoffice Server" {
                description "Core backend server managing BOOKS content logic and data"
                tag "Amazon Web Services - Elastic Beanstalk"
              }

              tvBackofficeServer = component "TV Backoffice Server" {
                description "Core backend server managing TV content logic and data"
                tag "Amazon Web Services - Elastic Beanstalk"
              }
          }

          apps = container "Kidjo Apps" {
            description "Kidjo mobile and web apps"

            accountsApp = component "Kidjo Accounts App" {
              description "Accounts section of the Kidjo App"
              tag "kidjo"
            }

            tvApp = component "Kidjo TV App" {
              description "TV version of the Kidjo App"
              tag "kidjoTV"
            }

            gamesApp = component "Kidjo Games App" {
              description "Games section of Kidjo"
              tag "kidjoGames"
            }

            storiesApp = component "Kidjo Stories App" {
              description "TV version of the Kidjo App"
              tag "kidjoStories"
            }
          }

          backOffice = container "Kidjo Backoffice App" {
            description "Games section of Kidjo"
          }

          analytics = container "Analytics" {
            description "External analytics services"

            gateway = component "AWS Gateway" {
              description "Analytics gateway"
              tag "Amazon Web Services - API Gateway"
            }

            sqs = component "AWS SQS" {
              description "Analytics SQS queue"
              tag "Amazon Web Services - Simple Queue Service"
            }

            amplitude = component "Amplitude" {
              description "Amplitude analytics service"
              tag "amplitude"
            }

            gateway -> sqs "write event in queue"
            sqs -> lambda "trigger lambda" "" "innerAnalyticsView"
            lambda -> s3 "store events in" "" "innerAnalyticsView"
            lambda -> amplitude "publish events in real time" "" "innerAnalyticsView"

          }

          ftp = container "FTP Server" {
            description "SFTP server"
            tag "Amazon Web Services - AWS Transfer Family AWS SFTP"

            sftp = component "AWS Transfer Family" {
              description "Transfer Family"
              tag "Amazon Web Services - AWS Transfer Family AWS SFTP"

            }
          }

          appUser -> apps.tvApp "Uses"
          appUser -> apps.gamesApp "Uses"
          appUser -> apps.storiesApp "Uses"

          portalUser -> backOffice "Uses"

          partnerUser -> partners.contentConsumer "Uses partners services"

          apps.accountsApp -> dnsService "Uses"
          apps.tvApp -> dnsService "Uses"
          apps.gamesApp -> dnsService "Uses"
          apps.storiesApp -> dnsService "Uses"
          backoffice -> dnsService "Uses"

          dnsService -> alb "Pass traffic"

          alb -> servers.tvBackofficeServer "forward the traffic based on host/path"
          alb -> servers.booksBackofficeServer "forward the traffic based on host/path"
          alb -> servers.kidjoStoriesServer "forward the traffic based on host/path"
          alb -> servers.kidjoGamesServer "forward the traffic based on host/path"
          alb -> servers.kidjoAppServer "forward the traffic based on host/path"

          apps -> analytics.gateway "submit events"
          apps -> cloudFront "Uses CDNs"
          backOffice -> cloudFront "Uses CDNs"
          cloudFront -> s3 "Serve content from"
          servers -> s3 "Write/Read content to/from"

          servers.kidjoAppServer -> rds.kidjoDB "Read/Write data"
          servers.kidjoGamesServer -> rds.kidjoGamesDB "Read/Write data"
          servers.kidjoStoriesServer -> rds.kidjoStoriesDB "Read/Write data"
          servers.tvBackofficeServer -> rds.kidjoBODB "Read/Write data"
          servers.booksBackofficeServer -> rds.kidjoBODB "Read/Write data"

          servers.tvBackofficeServer -> transcoder "Transcode content"

        }

        portalUser -> kidjo.ftp.sftp "Manage Content Files"
        partnerUser -> kidjo.ftp.sftp  "Upload Content Files"
        kidjo.ftp.sftp  -> kidjo.s3 "Store content" "" "innerFTPView"
        kidjo.s3  -> kidjo.lambda "trigger lambda" "" "innerFTPView"
        kidjo.lambda -> kidjo.rds.kidjoBODB "store incoming video" "" "innerFTPView"
        kidjo.lambda -> kidjo.s3 "copy files to destination bucket" "" "innerFTPView"

        appUser -> partners "Uses partners services"
        appUser -> partners.usersAcquisition "Uses partners services"

        appUser -> partners.usersAcquisition.twt "Uses TimWe services"
        appUser -> partners.usersAcquisition.switch "Uses Switch services"
        appUser -> partners.usersAcquisition.cafeyn "Uses Cafeyn services"
        appUser -> partners.usersAcquisition.salt "Uses Salt services"
        appUser -> partners.usersAcquisition.dv "Uses DV services"
        appUser -> partners.usersAcquisition.mondia "Uses Mondia services"


        partners.usersAcquisition.twt -> kidjo.servers.kidjoAppServer "Creates Subscriptions"
        partners.usersAcquisition.dv -> kidjo.servers.kidjoAppServer "Creates Subscriptions"
        partners.usersAcquisition.switch -> kidjo.servers.kidjoAppServer "Creates Subscriptions"
        partners.usersAcquisition.cafeyn -> kidjo.servers.kidjoAppServer "Creates Subscriptions"
        partners.usersAcquisition.mondia -> kidjo.servers.kidjoAppServer "Creates Subscriptions"
        partners.contentConsumer -> kidjo.servers.tvBackofficeServer "Consumes Content"

        partners.usersAcquisition.twt -> kidjo.apps "Redirects to"
        partners.usersAcquisition.switch -> kidjo.apps "Redirects to"
        partners.usersAcquisition.cafeyn -> kidjo.apps "Redirects to"
        partners.usersAcquisition.salt -> kidjo.apps "Redirects to"
        partners.usersAcquisition.dv -> kidjo.apps "Redirects to"
        partners.usersAcquisition.mondia -> kidjo.apps "Redirects to"
        partners.contentConsumer -> kidjo.apps "Redirects to"

        partners.usersAcquisition.salt -> kidjo.ftp "Manage subscriptions requests"

        kidjo.apps.accountsApp -> brainTree "Process payments"
        kidjo.servers.kidjoAppServer -> brainTree "Manage payments"
      }

      views {

        styles {
          element "person" {
            shape person
            fontSize 14
            stroke #34495e
            strokeWidth 2
          }
          element "gandi" {
            icon https://www.gandi.net/static/images/gandi-favicon-192.c33a4f624a0b.png
          }
          element "kidjo" {
            icon https://d3aod987c9rl70.cloudfront.net/images/kidjo_logo.png
          }
          element "kidjoTV" {
            icon https://d3aod987c9rl70.cloudfront.net/images/kidjo_tv.png
          }
          element "kidjoGames" {
            icon https://d3aod987c9rl70.cloudfront.net/images/kidjo_games.png
          }
          element "kidjoStories" {
            icon https://d3aod987c9rl70.cloudfront.net/images/kidjo_stories.png
          }
          element "amplitude" {
            icon https://cdn.prod.website-files.com/64da81538e9bdebe7ae2fa11/64ee69310bb55f013bd361a7_Amplitude%20Logo.svg
          }


          element "Software System" {
            background #1168bd
            color #ffffff
            shape RoundedBox
            fontSize 16
          }
          element "Container" {
            background #438dd5
            color #ffffff
            shape RoundedBox
            fontSize 14
          }
          element "Component" {
            background #85bbf0
            color #000000
            shape RoundedBox
            fontSize 12
          }
          relationship "Uses" {
            color #2e7d32
            thickness 3
            style solid
            routing Orthogonal
          }
          relationship "Integrates with" {
            color #d32f2f
            thickness 2
            style dashed
            routing Orthogonal
          }
          element "braintree" {
            icon https://upload.wikimedia.org/wikipedia/commons/0/00/Braintree-logo1.png
          }
        }

        systemContext kidjo {
          include *
          include partnerUser
          exclude kidjo.ftp.sftp
          autolayout lr
          title "System Context - Kidjo"
        }

        container kidjo {
          include *
          include brainTree
          exclude "relationship.tag==innerAnalyticsView"
          exclude "relationship.tag==innerFTPView"
          exclude "element.tag==innerView"
          exclude partners
          autolayout lr
          title "Container View - Kidjo System"
        }

        container partners {
          include appUser
          include partnerUser
          include partners.usersAcquisition
          include partners.contentConsumer
          include kidjo.servers
          include kidjo.servers
          include kidjo.ftp
          autolayout lr
          title "Container View - Kidjo Partners"
        }

        component partners.usersAcquisition {
            include appUser
            include partners.usersAcquisition.twt
            include partners.usersAcquisition.mondia
            include partners.usersAcquisition.switch
            include partners.usersAcquisition.cafeyn
            include partners.usersAcquisition.salt
            include partners.usersAcquisition.dv
            include kidjo.servers.kidjoAppServer
            include kidjo.ftp
            include kidjo.s3
            autolayout tb
            title "Component View - Users Acquisition Partners"
        }

        component kidjo.apps {
            include *
            include portalUser
            include kidjo.backOffice
            include kidjo.alb
            include kidjo.servers
            include brainTree
            autolayout lr
            title "Container View - Kidjo Applications"
        }

        component kidjo.analytics {
            include *
            include appUser
            include kidjo.s3
            exclude "relationship.tag==innerFTPView"
            autolayout lr
            title "Container View - Kidjo Analytics"
        }

        component kidjo.servers {
            include kidjo.servers.kidjoAppServer
            include kidjo.servers.kidjoGamesServer
            include kidjo.servers.kidjoStoriesServer
            include kidjo.servers.tvBackofficeServer
            include kidjo.servers.booksBackofficeServer
            include kidjo.rds.kidjoDB
            include kidjo.rds.kidjoGamesDB
            include kidjo.rds.kidjoStoriesDB
            include kidjo.rds.kidjoBODB
            include brainTree
            autolayout lr
            title "Container View - Kidjo Backend Services"
        }

        component kidjo.ftp {
            include portalUser
            include partnerUser
            include kidjo.ftp.sftp
            include kidjo.lambda
            include kidjo.s3
            include kidjo.rds.kidjoBODB
            exclude "relationship.tag==innerAnalyticsView"
            autolayout lr
            title "Container View - Kidjo FTP Server"
        }

        theme https://static.structurizr.com/themes/amazon-web-services-2023.01.31/icons.json
      }


}